package com.wonderslate.data

import grails.transaction.Transactional

@Transactional
class LiveMockTestsService {

    /**
     * Get all active (ongoing) mock tests
     * A test is active if current date is between test start date and test end date
     */
    def getActiveMockTests() {
        try {
            Date currentDate = new Date()

            def activeMockTests = LiveMockMst.executeQuery("""
                SELECT lm, rd FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND rd.testStartDate IS NOT NULL
                AND rd.testEndDate IS NOT NULL
                AND rd.testStartDate <= :currentDate
                AND rd.testEndDate > :currentDate
                ORDER BY rd.testStartDate DESC
            """, [currentDate: currentDate])

            def result = []
            activeMockTests.each { row ->
                def liveMockMst = row[0]
                def resourceDtl = row[1]
                result.add([
                    id: liveMockMst.id,
                    mcqResId: liveMockMst.mcqResId,
                    createdBy: liveMockMst.createdBy,
                    dateCreated: liveMockMst.dateCreated,
                    resourceName: resourceDtl.resourceName,
                    testStartDate: resourceDtl.testStartDate,
                    testEndDate: resourceDtl.testEndDate,
                    status: 'active'
                ])
            }

            return [status: 'success', data: result]
        } catch (Exception e) {
            log.error("Error getting active mock tests: ${e.message}", e)
            return [status: 'error', message: 'Failed to fetch active mock tests']
        }
    }

    /**
     * Get all completed mock tests
     * A test is completed if test end date is less than current date
     */
    def getCompletedMockTests() {
        try {
            Date currentDate = new Date()

            def completedMockTests = LiveMockMst.executeQuery("""
                SELECT lm, rd FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND rd.testEndDate IS NOT NULL
                AND rd.testEndDate < :currentDate
                ORDER BY rd.testEndDate DESC
            """, [currentDate: currentDate])

            def result = []
            completedMockTests.each { row ->
                def liveMockMst = row[0]
                def resourceDtl = row[1]
                result.add([
                    id: liveMockMst.id,
                    mcqResId: liveMockMst.mcqResId,
                    createdBy: liveMockMst.createdBy,
                    dateCreated: liveMockMst.dateCreated,
                    resourceName: resourceDtl.resourceName,
                    testStartDate: resourceDtl.testStartDate,
                    testEndDate: resourceDtl.testEndDate,
                    status: 'completed'
                ])
            }

            return [status: 'success', data: result]
        } catch (Exception e) {
            log.error("Error getting completed mock tests: ${e.message}", e)
            return [status: 'error', message: 'Failed to fetch completed mock tests']
        }
    }

    /**
     * Get all mock tests (active, completed, and upcoming)
     */
    def getAllMockTests() {
        try {
            Date currentDate = new Date()

            def allMockTests = LiveMockMst.executeQuery("""
                SELECT lm, rd FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                ORDER BY rd.testStartDate DESC
            """)

            def result = []
            allMockTests.each { row ->
                def liveMockMst = row[0]
                def resourceDtl = row[1]

                // Determine status
                String status = 'upcoming'
                if (resourceDtl.testStartDate && resourceDtl.testEndDate) {
                    if (resourceDtl.testEndDate < currentDate) {
                        status = 'completed'
                    } else if (resourceDtl.testStartDate <= currentDate && resourceDtl.testEndDate > currentDate) {
                        status = 'active'
                    }
                }

                result.add([
                    id: liveMockMst.id,
                    mcqResId: liveMockMst.mcqResId,
                    createdBy: liveMockMst.createdBy,
                    dateCreated: liveMockMst.dateCreated,
                    resourceName: resourceDtl.resourceName,
                    testStartDate: resourceDtl.testStartDate,
                    testEndDate: resourceDtl.testEndDate,
                    status: status
                ])
            }

            return [status: 'success', data: result]
        } catch (Exception e) {
            log.error("Error getting all mock tests: ${e.message}", e)
            return [status: 'error', message: 'Failed to fetch all mock tests']
        }
    }

    /**
     * Delete a mock test by setting isDeleted to true
     */
    def deleteMockTest(Long mockTestId, String deletedBy) {
        try {
            LiveMockMst liveMockMst = LiveMockMst.get(mockTestId)

            if (!liveMockMst) {
                return [status: 'error', message: 'Mock test not found']
            }

            if (liveMockMst.isDeleted == 'true') {
                return [status: 'error', message: 'Mock test is already deleted']
            }

            liveMockMst.isDeleted = 'true'
            liveMockMst.deletedBy = deletedBy
            liveMockMst.save(failOnError: true, flush: true)

            return [status: 'success', message: 'Mock test deleted successfully']
        } catch (Exception e) {
            log.error("Error deleting mock test: ${e.message}", e)
            return [status: 'error', message: 'Failed to delete mock test']
        }
    }
}
