package com.wonderslate.data
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import grails.converters.JSON

class LiveMockTestsController {
    def springSecurityService

    def index() { }

    @Secured(['ROLE_PDF_EXTRACTOR']) @Transactional
    def addMCQsToMockTest() {
        try {
            if(!params.mcqResId){
                render([status: "error", statusCode: 400, message: "MCQ Resource ID is required"] as JSON)
                return false
            }

            Long mcqResId = new Long(params.mcqResId)

            LiveMockMst liveMockMst = LiveMockMst.findByMcqResId(mcqResId)

            if(!liveMockMst){
                liveMockMst = new LiveMockMst(mcqResId: mcqResId, createdBy: springSecurityService.currentUser.username)
                liveMockMst.save(failOnError: true, flush: true)
            }else {
                if((liveMockMst.isDeleted == null || "false".equals(liveMockMst.isDeleted)) && isLiveMockActive(mcqResId)){
                    render([status: "error", statusCode: 400, message: "MCQs already added to mock test and is still active."] as JSON)
                    return false
                }
            }

            render([status: "success", statusCode: 200, message: "MCQs added to mock test"] as JSON)

        }catch (Exception e) {
            log.error("Error in addMCQsToMockTest: ${e.message}", e)
            render([status: "error", message: "Internal server error"] as JSON)
        }
    }

    private boolean isLiveMockActive(mcqResId) {
        boolean isLiveMockActive = false
        ResourceDtl resourceDtl = ResourceDtl.findById(mcqResId)
        if (resourceDtl!=null && resourceDtl.testStartDate!=null && resourceDtl.testEndDate!=null) {
            if(resourceDtl.testEndDate > new Date()){
                isLiveMockActive = true
            }
        }

        return isLiveMockActive
    }

    /*********
     - API to list all active (ongoing) mock tests
     - To check if a test is active, we check if the test end date is greater than the current date and time along
        with the test start date being less than the current date and time.
     ***********/

    /*********
     - API to list all completed mock tests
     - To check if a test is completed, we check if the test end date is less than the current date and time.
     ***********/


    /*********
     - API to list all tests
     ***********/


    /*********
     - API to delete a mock test
     - To delete a mock test, we update the isDeleted field to true.
     ***********/


}
